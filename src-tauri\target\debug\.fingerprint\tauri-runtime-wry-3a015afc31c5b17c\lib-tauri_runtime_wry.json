{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 14648588457211574579, "deps": [[376837177317575824, "softbuffer", false, 3733334207863867574], [442785307232013896, "tauri_runtime", false, 10075173648867670912], [3150220818285335163, "url", false, 14021696919749951379], [3722963349756955755, "once_cell", false, 17797913802642721045], [4143744114649553716, "raw_window_handle", false, 6880312038169398310], [5986029879202738730, "log", false, 5130091804064732190], [7752760652095876438, "build_script_build", false, 4727651248826155669], [8539587424388551196, "webview2_com", false, 6498748656415624695], [9010263965687315507, "http", false, 11912463509730526472], [11050281405049894993, "tauri_utils", false, 18027016555269460940], [13116089016666501665, "windows", false, 2343488490810939236], [13223659721939363523, "tao", false, 7287264587338865569], [14794439852947137341, "wry", false, 2933052560943625134]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-3a015afc31c5b17c\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}