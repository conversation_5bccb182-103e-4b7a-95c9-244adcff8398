/**
 * Componente HorizontalTabBar
 * Barra de navegación horizontal con pestañas para la aplicación Hydra21
 * Incluye las pestañas: Inicio, IA, Carpetas, Herramientas
 */

import React, { useState } from 'react';
import {
  Home,
  Brain,
  FolderOpen,
  Wrench,
  ChevronRight
} from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';

// Definición de tipos para las pestañas
export interface TabItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  content?: React.ReactNode;
  disabled?: boolean;
}

interface HorizontalTabBarProps {
  className?: string;
  onTabChange?: (tabId: string) => void;
  defaultTab?: string;
}

// Configuración de las pestañas principales
const defaultTabs: TabItem[] = [
  {
    id: 'inicio',
    label: 'Inicio',
    icon: <Home size={16} />,
  },
  {
    id: 'ia',
    label: 'IA',
    icon: <Brain size={16} />,
  },
  {
    id: 'carpetas',
    label: 'Carpetas',
    icon: <FolderOpen size={16} />,
  },
  {
    id: 'herramientas',
    label: 'Herramientas',
    icon: <Wrench size={16} />,
  },
];

export const HorizontalTabBar: React.FC<HorizontalTabBarProps> = ({
  className = '',
  onTabChange,
  defaultTab = 'inicio'
}) => {
  const { isDark } = useTheme();
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleTabClick = (tabId: string) => {
    if (activeTab !== tabId) {
      setActiveTab(tabId);
      onTabChange?.(tabId);
    }
  };

  // Estilos base para la barra de pestañas con diseño Raycast-inspired
  const tabBarClasses = `
    flex items-center backdrop-blur-md transition-all duration-300 relative z-30
    ${isDark
      ? 'bg-gray-900/80 border-b border-gray-800/50'
      : 'bg-white/80 border-b border-gray-200/50'
    }
    ${className}
  `.trim();

  // Estilos para cada pestaña - Raycast-inspired
  const getTabClasses = (tabId: string, disabled?: boolean) => {
    const isActive = activeTab === tabId;

    if (disabled) {
      return `
        flex items-center gap-2.5 px-5 py-3 text-sm font-medium rounded-lg mx-1
        opacity-50 cursor-not-allowed
        ${isDark ? 'text-gray-500' : 'text-gray-400'}
      `.trim();
    }

    return `
      flex items-center gap-2.5 px-5 py-3 text-sm font-medium rounded-lg mx-1
      transition-all duration-200 ease-in-out cursor-pointer backdrop-blur-sm
      ${isActive
        ? `${isDark
            ? 'text-white bg-gray-800/80 shadow-lg border border-gray-700/50'
            : 'text-gray-900 bg-gray-100/80 shadow-md border border-gray-200/60'
          }`
        : `${isDark
            ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/40'
            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/40'
          } hover:shadow-sm`
      }
      focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2
      active:scale-[0.98]
    `.trim();
  };

  return (
    <div className={tabBarClasses}>
      {/* Espaciador izquierdo */}
      <div className="flex-1" />

      {/* Contenedor de pestañas centradas */}
      <div className="flex items-center">
        {defaultTabs.map((tab) => (
          <button
            key={tab.id}
            className={getTabClasses(tab.id, tab.disabled)}
            onClick={() => !tab.disabled && handleTabClick(tab.id)}
            disabled={tab.disabled}
            role="tab"
            aria-selected={activeTab === tab.id}
            aria-controls={`tabpanel-${tab.id}`}
          >
            {/* Ícono de la pestaña */}
            <span className="flex-shrink-0">
              {tab.icon}
            </span>

            {/* Etiqueta de la pestaña */}
            <span className="whitespace-nowrap font-medium">
              {tab.label}
            </span>
          </button>
        ))}
      </div>

      {/* Espaciador derecho con indicador de navegación */}
      <div className="flex-1 flex justify-end">
        {/* Indicador de navegación sutil */}
        <div className={`
          flex items-center px-4 text-xs font-medium
          ${isDark ? 'text-gray-600' : 'text-gray-500'}
        `}>
          <ChevronRight size={10} className="mr-1.5 opacity-60" />
          <span className="capitalize tracking-wide">{activeTab}</span>
        </div>
      </div>
    </div>
  );
};

// Hook personalizado para gestionar el estado de las pestañas
export const useTabNavigation = (initialTab: string = 'inicio') => {
  const [currentTab, setCurrentTab] = useState(initialTab);

  const navigateToTab = (tabId: string) => {
    setCurrentTab(tabId);
  };

  const isTabActive = (tabId: string) => currentTab === tabId;

  return {
    currentTab,
    navigateToTab,
    isTabActive,
  };
};

export default HorizontalTabBar;
