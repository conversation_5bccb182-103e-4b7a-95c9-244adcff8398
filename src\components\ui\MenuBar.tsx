/**
 * Componente MenuBar
 * Barra de menú tradicional con elementos desplegables (File, Edit, View, Help)
 */

import React, { useState, useRef, useEffect } from 'react';
import type { MenuBarItem, MenuItem } from '../../types/titlebar';
import { useTheme } from '../../hooks/useTheme';
import { ChevronRight } from 'lucide-react';

interface MenuBarProps {
  items: MenuBarItem[];
  onMenuItemClick?: (menuId: string, itemId: string) => void;
  className?: string;
}

interface DropdownMenuProps {
  items: MenuItem[];
  isOpen: boolean;
  onClose: () => void;
  onItemClick: (item: MenuItem) => void;
  position: { x: number; y: number };
}

const DropdownMenu: React.FC<DropdownMenuProps> = ({
  items,
  isOpen,
  onClose,
  onItemClick,
  position
}) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const { isDark } = useTheme();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleItemClick = (item: MenuItem) => {
    if (!item.disabled && item.action) {
      item.action();
      onItemClick(item);
      onClose();
    }
  };

  return (
    <div
      ref={menuRef}
      className={`
        fixed z-[100] min-w-48 py-1 rounded-md shadow-lg border
        ${isDark
          ? 'bg-gray-800 border-gray-700 text-gray-100'
          : 'bg-white border-gray-200 text-gray-900'
        }
        animate-in fade-in slide-in duration-150
      `}
      style={{
        left: position.x,
        top: position.y,
      }}
      role="menu"
      aria-orientation="vertical"
    >
      {items.map((item) => (
        <React.Fragment key={item.id}>
          {item.separator ? (
            <div
              className={`
                my-1 h-px
                ${isDark ? 'bg-gray-700' : 'bg-gray-200'}
              `}
              role="separator"
            />
          ) : (
            <button
              className={`
                w-full px-4 py-2 text-left text-sm transition-colors flex items-center justify-between
                ${item.disabled
                  ? 'opacity-50 cursor-not-allowed'
                  : `hover:${isDark ? 'bg-gray-700' : 'bg-gray-100'} focus:${isDark ? 'bg-gray-700' : 'bg-gray-100'}`
                }
                focus:outline-none
              `}
              onClick={() => handleItemClick(item)}
              disabled={item.disabled}
              role="menuitem"
              aria-label={item.label}
            >
              <div className="flex items-center gap-2">
                {item.icon && <item.icon size={16} />}
                <span>{item.label}</span>
              </div>
              <div className="flex items-center gap-2">
                {item.shortcut && (
                  <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    {item.shortcut}
                  </span>
                )}
                {item.submenu && <ChevronRight size={14} />}
              </div>
            </button>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export const MenuBar: React.FC<MenuBarProps> = ({
  items,
  onMenuItemClick,
  className = ""
}) => {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const { isDark } = useTheme();
  const menuRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  const handleMenuClick = (menuId: string, event: React.MouseEvent<HTMLButtonElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setMenuPosition({
      x: rect.left,
      y: rect.bottom
    });

    if (activeMenu === menuId) {
      setActiveMenu(null);
    } else {
      setActiveMenu(menuId);
    }
  };

  const handleMenuItemClick = (menuId: string, item: MenuItem) => {
    if (onMenuItemClick) {
      onMenuItemClick(menuId, item.id);
    }
    setActiveMenu(null);
  };

  const closeMenu = () => {
    setActiveMenu(null);
  };

  return (
    <div className={`flex items-center ${className}`}>
      {items.map((menu) => (
        <div key={menu.id} className="relative">
          <button
            ref={(el) => { menuRefs.current[menu.id] = el; }}
            className={`
              px-2 py-1 text-xs transition-colors rounded-sm
              ${activeMenu === menu.id
                ? `${isDark ? 'bg-gray-700 text-gray-100' : 'bg-gray-200 text-gray-900'}`
                : `hover:${isDark ? 'bg-gray-700/50' : 'bg-gray-100/50'} ${isDark ? 'text-gray-300' : 'text-gray-700'}`
              }
              focus:outline-none focus:ring-1 focus:ring-blue-500 focus:ring-opacity-50
            `}
            onClick={(e) => handleMenuClick(menu.id, e)}
            aria-expanded={activeMenu === menu.id}
            aria-haspopup="true"
            role="menuitem"
          >
            {menu.label}
          </button>

          <DropdownMenu
            items={menu.items}
            isOpen={activeMenu === menu.id}
            onClose={closeMenu}
            onItemClick={(item) => handleMenuItemClick(menu.id, item)}
            position={menuPosition}
          />
        </div>
      ))}
    </div>
  );
};
