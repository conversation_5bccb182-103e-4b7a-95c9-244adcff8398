/**
 * Componente OnboardingFlow
 * Flujo de introducción para nuevos usuarios de Hydra21
 */

import React, { useState } from 'react';
import { 
  ChevronLeft, 
  ChevronRight, 
  Home, 
  Brain, 
  FolderOpen, 
  Wrench,
  X,
  Check
} from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';

interface OnboardingFlowProps {
  onComplete: () => void;
  onSkip: () => void;
}

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

export const OnboardingFlow: React.FC<OnboardingFlowProps> = ({
  onComplete,
  onSkip
}) => {
  const { isDark } = useTheme();
  const [currentStep, setCurrentStep] = useState(0);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Bienvenido a Hydra21',
      description: 'Tu plataforma integral para ingeniería hidráulica y gestión de proyectos',
      icon: <Home size={48} className="text-blue-500" />,
      content: (
        <div className="text-center">
          <div className="mb-6">
            <div className={`
              w-32 h-32 mx-auto rounded-3xl flex items-center justify-center
              ${isDark 
                ? 'bg-gradient-to-br from-blue-500/20 to-purple-600/20 border border-blue-500/30' 
                : 'bg-gradient-to-br from-blue-100 to-purple-100 border border-blue-200'
              }
            `}>
              <Home size={64} className="text-blue-500" />
            </div>
          </div>
          <p className="text-lg text-muted-foreground">
            Descubre las herramientas que te ayudarán a optimizar tus proyectos hidráulicos
          </p>
        </div>
      )
    },
    {
      id: 'navigation',
      title: 'Navegación Intuitiva',
      description: 'Accede fácilmente a todas las funciones desde la barra de pestañas',
      icon: <FolderOpen size={48} className="text-green-500" />,
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {[
              { icon: <Home size={24} />, label: 'Inicio', color: 'blue' },
              { icon: <Brain size={24} />, label: 'IA', color: 'purple' },
              { icon: <FolderOpen size={24} />, label: 'Carpetas', color: 'green' },
              { icon: <Wrench size={24} />, label: 'Herramientas', color: 'orange' }
            ].map((tab, index) => (
              <div key={index} className={`
                p-4 rounded-lg border transition-all duration-300
                ${isDark ? 'glass-card-dark' : 'glass-card'}
                hover:scale-105
              `}>
                <div className={`text-${tab.color}-500 mb-2`}>
                  {tab.icon}
                </div>
                <span className="text-sm font-medium">{tab.label}</span>
              </div>
            ))}
          </div>
          <p className="text-sm text-muted-foreground text-center">
            Cada pestaña te lleva a una sección especializada de la aplicación
          </p>
        </div>
      )
    },
    {
      id: 'ai-assistant',
      title: 'Asistente de IA',
      description: 'Obtén ayuda inteligente para tus cálculos y diseños hidráulicos',
      icon: <Brain size={48} className="text-purple-500" />,
      content: (
        <div className="space-y-4">
          <div className={`
            p-6 rounded-lg
            ${isDark ? 'glass-card-dark' : 'glass-card'}
          `}>
            <div className="flex items-start space-x-4">
              <div className={`
                p-3 rounded-full
                ${isDark ? 'bg-purple-500/20' : 'bg-purple-100'}
              `}>
                <Brain size={24} className="text-purple-500" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold mb-2">Capacidades de IA</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Optimización de diseños hidráulicos</li>
                  <li>• Análisis predictivo de flujos</li>
                  <li>• Detección de anomalías</li>
                  <li>• Recomendaciones inteligentes</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'tools',
      title: 'Herramientas Especializadas',
      description: 'Suite completa de calculadoras y herramientas para ingeniería hidráulica',
      icon: <Wrench size={48} className="text-orange-500" />,
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-3">
            {[
              { name: 'Redes de Distribución', desc: 'Diseño y análisis de sistemas' },
              { name: 'Calculadoras Hidráulicas', desc: 'Herramientas de cálculo especializadas' },
              { name: 'Gestión de Proyectos', desc: 'Organiza y administra tus trabajos' }
            ].map((tool, index) => (
              <div key={index} className={`
                p-4 rounded-lg border transition-all duration-300
                ${isDark ? 'glass-effect-dark' : 'glass-effect'}
                hover:scale-102
              `}>
                <div className="flex items-center space-x-3">
                  <Wrench size={20} className="text-orange-500" />
                  <div>
                    <h5 className="font-medium">{tool.name}</h5>
                    <p className="text-xs text-muted-foreground">{tool.desc}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )
    }
  ];

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;

  const handleNext = () => {
    if (isLastStep) {
      onComplete();
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  return (
    <div className={`
      fixed inset-0 z-50 flex items-center justify-center p-4
      ${isDark 
        ? 'bg-gray-900/95 backdrop-blur-sm' 
        : 'bg-white/95 backdrop-blur-sm'
      }
    `}>
      <div className={`
        relative w-full max-w-2xl rounded-2xl p-8
        ${isDark ? 'glass-card-dark' : 'glass-card'}
        animate-in fade-in slide-in duration-500
      `}>
        {/* Botón de cerrar */}
        <button
          onClick={onSkip}
          className={`
            absolute top-4 right-4 p-2 rounded-full
            ${isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}
            transition-colors
          `}
        >
          <X size={20} />
        </button>

        {/* Indicador de progreso */}
        <div className="mb-8">
          <div className="flex space-x-2 mb-4">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`
                  h-2 flex-1 rounded-full transition-all duration-300
                  ${index <= currentStep
                    ? (isDark ? 'bg-blue-500' : 'bg-blue-600')
                    : (isDark ? 'bg-gray-700' : 'bg-gray-200')
                  }
                `}
              />
            ))}
          </div>
          <div className="text-sm text-muted-foreground text-center">
            Paso {currentStep + 1} de {steps.length}
          </div>
        </div>

        {/* Contenido del paso */}
        <div className="text-center mb-8">
          <div className="mb-6">
            {currentStepData.icon}
          </div>
          <h2 className="text-2xl font-bold mb-2">{currentStepData.title}</h2>
          <p className="text-muted-foreground mb-6">{currentStepData.description}</p>
          <div className="max-w-lg mx-auto">
            {currentStepData.content}
          </div>
        </div>

        {/* Botones de navegación */}
        <div className="flex justify-between items-center">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className={`
              flex items-center space-x-2 px-4 py-2 rounded-lg
              transition-all duration-200
              ${currentStep === 0
                ? 'opacity-50 cursor-not-allowed'
                : `${isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`
              }
            `}
          >
            <ChevronLeft size={20} />
            <span>Anterior</span>
          </button>

          <button
            onClick={onSkip}
            className={`
              px-4 py-2 rounded-lg text-sm
              ${isDark ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}
              transition-colors
            `}
          >
            Omitir
          </button>

          <button
            onClick={handleNext}
            className={`
              flex items-center space-x-2 px-6 py-2 rounded-lg
              ${isDark 
                ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                : 'bg-blue-600 hover:bg-blue-700 text-white'
              }
              transition-all duration-200 hover:scale-105
            `}
          >
            <span>{isLastStep ? 'Comenzar' : 'Siguiente'}</span>
            {isLastStep ? <Check size={20} /> : <ChevronRight size={20} />}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OnboardingFlow;
