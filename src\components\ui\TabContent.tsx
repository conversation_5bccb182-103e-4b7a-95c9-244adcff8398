/**
 * Componentes de contenido para cada pestaña
 * Inicio, IA, Carpetas, Herramientas
 */

import React from 'react';
import {
  Home,
  Brain,
  FolderOpen,
  Wrench,
  Sparkles,
  FileText,
  Settings,
  Zap,
  Database,
  Code,
  Cpu,
  Network
} from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';

interface TabContentProps {
  tabId: string;
  className?: string;
}

// Componente para la pestaña Inicio
const InicioContent: React.FC = () => {
  const { isDark } = useTheme();

  return (
    <div className="p-6 space-y-6">
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className={`
            p-4 rounded-full
            ${isDark ? 'bg-blue-500/20 text-blue-400' : 'bg-blue-100 text-blue-600'}
          `}>
            <Home size={32} />
          </div>
        </div>
        <h2 className="text-2xl font-bold mb-2">Bienvenido a Hydra21</h2>
        <p className="text-muted-foreground">
          Tu plataforma integral para ingeniería hidráulica y gestión de proyectos
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <FileText size={20} className="text-blue-500" />
            <h3 className="font-semibold">Proyectos Recientes</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Accede rápidamente a tus proyectos más recientes
          </p>
        </div>

        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <Sparkles size={20} className="text-purple-500" />
            <h3 className="font-semibold">Asistente IA</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Obtén ayuda inteligente para tus cálculos hidráulicos
          </p>
        </div>

        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <Settings size={20} className="text-green-500" />
            <h3 className="font-semibold">Herramientas</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Acceso directo a calculadoras y herramientas especializadas
          </p>
        </div>
      </div>
    </div>
  );
};

// Componente para la pestaña IA
const IAContent: React.FC = () => {
  const { isDark } = useTheme();

  return (
    <div className="p-6 space-y-6">
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className={`
            p-4 rounded-full
            ${isDark ? 'bg-purple-500/20 text-purple-400' : 'bg-purple-100 text-purple-600'}
          `}>
            <Brain size={32} />
          </div>
        </div>
        <h2 className="text-2xl font-bold mb-2">Asistente de Inteligencia Artificial</h2>
        <p className="text-muted-foreground">
          Potencia tus proyectos con IA especializada en ingeniería hidráulica
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={`
          rounded-lg p-6 transition-all duration-300
          ${isDark ? 'glass-card-dark' : 'glass-card'}
        `}>
          <div className="flex items-center gap-3 mb-4">
            <Cpu size={24} className="text-purple-500" />
            <h3 className="text-lg font-semibold">Análisis Inteligente</h3>
          </div>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• Optimización de diseños hidráulicos</li>
            <li>• Análisis predictivo de flujos</li>
            <li>• Detección de anomalías en sistemas</li>
            <li>• Recomendaciones de mejora</li>
          </ul>
        </div>

        <div className={`
          rounded-lg p-6 transition-all duration-300
          ${isDark ? 'glass-card-dark' : 'glass-card'}
        `}>
          <div className="flex items-center gap-3 mb-4">
            <Zap size={24} className="text-yellow-500" />
            <h3 className="text-lg font-semibold">Automatización</h3>
          </div>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• Generación automática de reportes</li>
            <li>• Cálculos hidráulicos avanzados</li>
            <li>• Modelado 3D asistido</li>
            <li>• Validación de diseños</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

// Componente para la pestaña Carpetas
const CarpetasContent: React.FC = () => {
  const { isDark } = useTheme();

  return (
    <div className="p-6 space-y-6">
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className={`
            p-4 rounded-full
            ${isDark ? 'bg-green-500/20 text-green-400' : 'bg-green-100 text-green-600'}
          `}>
            <FolderOpen size={32} />
          </div>
        </div>
        <h2 className="text-2xl font-bold mb-2">Gestión de Archivos</h2>
        <p className="text-muted-foreground">
          Organiza y gestiona todos tus proyectos y documentos
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <Database size={20} className="text-blue-500" />
            <h3 className="font-semibold">Proyectos</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Gestiona tus proyectos hidráulicos
          </p>
        </div>

        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <FileText size={20} className="text-orange-500" />
            <h3 className="font-semibold">Documentos</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Almacena planos y especificaciones
          </p>
        </div>

        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <Code size={20} className="text-purple-500" />
            <h3 className="font-semibold">Modelos</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Modelos 3D y simulaciones
          </p>
        </div>
      </div>
    </div>
  );
};

// Componente para la pestaña Herramientas
const HerramientasContent: React.FC = () => {
  const { isDark } = useTheme();

  return (
    <div className="p-6 space-y-6">
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className={`
            p-4 rounded-full
            ${isDark ? 'bg-orange-500/20 text-orange-400' : 'bg-orange-100 text-orange-600'}
          `}>
            <Wrench size={32} />
          </div>
        </div>
        <h2 className="text-2xl font-bold mb-2">Herramientas Especializadas</h2>
        <p className="text-muted-foreground">
          Suite completa de herramientas para ingeniería hidráulica
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <Network size={20} className="text-blue-500" />
            <h3 className="font-semibold">Redes de Distribución</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Diseño y análisis de redes hidráulicas
          </p>
        </div>

        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <Zap size={20} className="text-yellow-500" />
            <h3 className="font-semibold">Calculadoras</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Herramientas de cálculo hidráulico
          </p>
        </div>

        <div className={`
          rounded-lg p-4 transition-all duration-300 cursor-pointer
          ${isDark ? 'glass-card-dark glass-hover-dark' : 'glass-card glass-hover'}
        `}>
          <div className="flex items-center gap-3 mb-3">
            <Settings size={20} className="text-gray-500" />
            <h3 className="font-semibold">Configuración</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Ajustes y preferencias del sistema
          </p>
        </div>
      </div>
    </div>
  );
};

// Componente principal que renderiza el contenido según la pestaña activa
export const TabContent: React.FC<TabContentProps> = ({ tabId, className = '' }) => {
  const renderContent = () => {
    switch (tabId) {
      case 'inicio':
        return <InicioContent />;
      case 'ia':
        return <IAContent />;
      case 'carpetas':
        return <CarpetasContent />;
      case 'herramientas':
        return <HerramientasContent />;
      default:
        return <InicioContent />;
    }
  };

  return (
    <div className={`flex-1 overflow-auto ${className}`} role="tabpanel" id={`tabpanel-${tabId}`}>
      {renderContent()}
    </div>
  );
};

export default TabContent;
