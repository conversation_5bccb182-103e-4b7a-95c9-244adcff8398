{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 17459901917850216397, "deps": [[442785307232013896, "build_script_build", false, 2209882325157078724], [3150220818285335163, "url", false, 14021696919749951379], [4143744114649553716, "raw_window_handle", false, 6880312038169398310], [7606335748176206944, "dpi", false, 5198814162836119677], [9010263965687315507, "http", false, 11912463509730526472], [9689903380558560274, "serde", false, 11858664821236707062], [10806645703491011684, "thiserror", false, 4474893319039160182], [11050281405049894993, "tauri_utils", false, 18027016555269460940], [13116089016666501665, "windows", false, 2343488490810939236], [15367738274754116744, "serde_json", false, 18252066544345443454], [16727543399706004146, "cookie", false, 9191178620271352337]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-09ba49851154395e\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}