/**
 * Store para el sistema de notificaciones
 * Maneja notificaciones toast, centro de notificaciones y configuración
 */

import type {
  Notification,
  ToastNotification,
  NotificationCenter,
  NotificationSettings,
  NotificationType
} from '../types/notifications';

import {
  NOTIFICATION_DURATIONS
} from '../types/notifications';

// Estado inicial
const initialState = {
  notifications: [] as Notification[],
  toasts: [] as ToastNotification[],
  center: {
    notifications: [],
    unreadCount: 0,
    isOpen: false,
    filter: 'all' as NotificationType | 'all',
    sortBy: 'newest' as 'newest' | 'oldest' | 'type',
  } as NotificationCenter,
  settings: {
    enableToasts: true,
    enableSounds: false,
    enableSystemNotifications: true,
    autoCloseDelay: 5000,
    maxNotifications: 50,
    groupSimilar: true,
    showTimestamps: true,
  } as NotificationSettings,
};

// Listeners para cambios de estado
const listeners = new Set<() => void>();

// Estado global
let state = { ...initialState };

/**
 * Genera un ID único para notificaciones
 */
const generateId = (): string => {
  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Notifica a todos los listeners sobre cambios de estado
 */
const notifyListeners = () => {
  listeners.forEach(listener => listener());
};

/**
 * Actualiza el estado y notifica cambios
 */
const updateState = (updates: Partial<typeof state>) => {
  state = { ...state, ...updates };

  // Actualizar centro de notificaciones
  if (updates.notifications) {
    state.center = {
      ...state.center,
      notifications: updates.notifications,
      unreadCount: updates.notifications.filter(n => !n.read).length,
    };
  }

  notifyListeners();
};

/**
 * Guarda las configuraciones en localStorage
 */
const saveSettings = (settings: NotificationSettings) => {
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem('notification-settings', JSON.stringify(settings));
  }
};

/**
 * Carga las configuraciones desde localStorage
 */
const loadSettings = (): NotificationSettings => {
  if (typeof localStorage !== 'undefined') {
    const saved = localStorage.getItem('notification-settings');
    if (saved) {
      try {
        return { ...initialState.settings, ...JSON.parse(saved) };
      } catch (error) {
        console.error('Error loading notification settings:', error);
      }
    }
  }
  return initialState.settings;
};

/**
 * Muestra notificación del sistema si está habilitada
 */
const showSystemNotification = (notification: Notification) => {
  if (!state.settings.enableSystemNotifications) return;

  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(notification.title, {
      body: notification.message,
      icon: '/pipe_logo.svg',
      tag: notification.id,
    });
  }
};

/**
 * Reproduce sonido de notificación si está habilitado
 */
const playNotificationSound = (_type: NotificationType) => {
  if (!state.settings.enableSounds) return;

  // Aquí se puede implementar la reproducción de sonidos específicos por tipo
  // Por ahora, usamos el sonido del sistema
  if ('Audio' in window) {
    try {
      const audio = new Audio();
      // Se pueden agregar diferentes sonidos por tipo
      audio.play().catch(() => {
        // Silenciar errores de audio si no se puede reproducir
      });
    } catch (_error) {
      // Silenciar errores de audio
    }
  }
};

// Store de notificaciones
export const notificationStore = {
  // Getters
  get notifications() { return state.notifications; },
  get toasts() { return state.toasts; },
  get center() { return state.center; },
  get settings() { return state.settings; },
  get unreadCount() { return state.center.unreadCount; },

  // Acciones principales
  addNotification: (notificationData: Omit<Notification, 'id' | 'timestamp' | 'read'>): string => {
    const notification: Notification = {
      ...notificationData,
      id: generateId(),
      timestamp: new Date(),
      read: false,
    };

    const newNotifications = [notification, ...state.notifications];

    // Limitar número máximo de notificaciones
    if (newNotifications.length > state.settings.maxNotifications) {
      newNotifications.splice(state.settings.maxNotifications);
    }

    updateState({ notifications: newNotifications });

    // Mostrar notificación del sistema
    showSystemNotification(notification);

    // Reproducir sonido
    playNotificationSound(notification.type);

    return notification.id;
  },

  removeNotification: (id: string) => {
    const newNotifications = state.notifications.filter(n => n.id !== id);
    updateState({ notifications: newNotifications });
  },

  markAsRead: (id: string) => {
    const newNotifications = state.notifications.map(n =>
      n.id === id ? { ...n, read: true } : n
    );
    updateState({ notifications: newNotifications });
  },

  markAllAsRead: () => {
    const newNotifications = state.notifications.map(n => ({ ...n, read: true }));
    updateState({ notifications: newNotifications });
  },

  clearAll: () => {
    updateState({ notifications: [], toasts: [] });
  },

  clearByType: (type: NotificationType) => {
    const newNotifications = state.notifications.filter(n => n.type !== type);
    const newToasts = state.toasts.filter(t => t.type !== type);
    updateState({ notifications: newNotifications, toasts: newToasts });
  },

  // Toast específicos
  showToast: (toastData: Omit<ToastNotification, 'id' | 'timestamp'>): string => {
    if (!state.settings.enableToasts) return '';

    const toast: ToastNotification = {
      ...toastData,
      id: generateId(),
      timestamp: new Date(),
      duration: toastData.duration || NOTIFICATION_DURATIONS[toastData.type],
      autoClose: toastData.autoClose !== false,
      position: toastData.position || 'top-right',
    };

    const newToasts = [...state.toasts, toast];
    updateState({ toasts: newToasts });

    // Auto-cerrar toast si está configurado
    if (toast.autoClose && toast.duration) {
      setTimeout(() => {
        notificationStore.hideToast(toast.id);
      }, toast.duration);
    }

    return toast.id;
  },

  hideToast: (id: string) => {
    const newToasts = state.toasts.filter(t => t.id !== id);
    updateState({ toasts: newToasts });
  },

  // Centro de notificaciones
  toggleCenter: () => {
    updateState({
      center: { ...state.center, isOpen: !state.center.isOpen }
    });
  },

  setFilter: (filter: NotificationType | 'all') => {
    updateState({
      center: { ...state.center, filter }
    });
  },

  setSortBy: (sortBy: 'newest' | 'oldest' | 'type') => {
    updateState({
      center: { ...state.center, sortBy }
    });
  },

  // Configuración
  updateSettings: (newSettings: Partial<NotificationSettings>) => {
    const updatedSettings = { ...state.settings, ...newSettings };
    updateState({ settings: updatedSettings });
    saveSettings(updatedSettings);
  },

  // Inicialización
  initialize: () => {
    const savedSettings = loadSettings();
    updateState({ settings: savedSettings });

    // Solicitar permisos para notificaciones del sistema
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  },
};

/**
 * Hook para suscribirse a cambios del store
 */
export const subscribeToNotifications = (callback: () => void) => {
  listeners.add(callback);

  // Retornar función de limpieza
  return () => {
    listeners.delete(callback);
  };
};

/**
 * Obtiene el estado actual de notificaciones
 */
export const getNotificationState = () => ({ ...state });
