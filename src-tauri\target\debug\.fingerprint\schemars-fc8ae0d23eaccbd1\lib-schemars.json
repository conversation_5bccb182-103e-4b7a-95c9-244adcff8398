{"rustc": 10895048813736897673, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 213508554779096394, "deps": [[3150220818285335163, "url", false, 13544346878586898129], [6913375703034175521, "build_script_build", false, 16273708575231197006], [8319709847752024821, "uuid1", false, 16227298859139089366], [9122563107207267705, "dyn_clone", false, 17784565293603956803], [9689903380558560274, "serde", false, 18397262076831661313], [14923790796823607459, "indexmap", false, 1089830593067836454], [15367738274754116744, "serde_json", false, 16284042609359377599], [16071897500792579091, "schemars_derive", false, 10314647760756835595]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-fc8ae0d23eaccbd1\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}