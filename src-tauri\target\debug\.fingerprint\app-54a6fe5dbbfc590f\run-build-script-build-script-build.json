{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 3516024209685369543], [10755362358622467486, "build_script_build", false, 5285384472013781966], [7236291379133587555, "build_script_build", false, 7187880481803698250]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-54a6fe5dbbfc590f\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}