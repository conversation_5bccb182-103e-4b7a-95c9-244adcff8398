/**
 * Utilidades de almacenamiento persistente para Hydra21
 * Maneja el almacenamiento local de configuraciones y estado de la aplicación
 */

// Claves de almacenamiento
export const STORAGE_KEYS = {
  ONBOARDING_COMPLETED: 'hydra21_onboarding_completed',
  USER_PREFERENCES: 'hydra21_user_preferences',
  THEME_SETTINGS: 'hydra21_theme_settings',
  NOTIFICATION_SETTINGS: 'hydra21_notification_settings',
  LAST_ACTIVE_TAB: 'hydra21_last_active_tab',
  WINDOW_STATE: 'hydra21_window_state',
} as const;

// Tipos para las configuraciones
export interface UserPreferences {
  language: 'es' | 'en';
  autoSave: boolean;
  showWelcomeMessage: boolean;
  defaultTab: string;
  compactMode: boolean;
}

export interface ThemeSettings {
  mode: 'light' | 'dark' | 'system';
  accentColor: string;
  fontSize: 'small' | 'medium' | 'large';
  animations: boolean;
}

export interface WindowState {
  width: number;
  height: number;
  x?: number;
  y?: number;
  maximized: boolean;
}

// Configuraciones por defecto
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  language: 'es',
  autoSave: true,
  showWelcomeMessage: true,
  defaultTab: 'inicio',
  compactMode: false,
};

export const DEFAULT_THEME_SETTINGS: ThemeSettings = {
  mode: 'system',
  accentColor: '#3b82f6',
  fontSize: 'medium',
  animations: true,
};

export const DEFAULT_WINDOW_STATE: WindowState = {
  width: 1200,
  height: 800,
  maximized: false,
};

/**
 * Clase para manejar el almacenamiento persistente
 */
class StorageManager {
  private isAvailable: boolean;

  constructor() {
    this.isAvailable = this.checkStorageAvailability();
  }

  /**
   * Verifica si localStorage está disponible
   */
  private checkStorageAvailability(): boolean {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return false;
      }
      
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Guarda un valor en el almacenamiento local
   */
  setItem<T>(key: string, value: T): boolean {
    if (!this.isAvailable) {
      console.warn('localStorage no está disponible');
      return false;
    }

    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(key, serializedValue);
      return true;
    } catch (error) {
      console.error('Error al guardar en localStorage:', error);
      return false;
    }
  }

  /**
   * Obtiene un valor del almacenamiento local
   */
  getItem<T>(key: string, defaultValue?: T): T | null {
    if (!this.isAvailable) {
      return defaultValue || null;
    }

    try {
      const item = localStorage.getItem(key);
      if (item === null) {
        return defaultValue || null;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error('Error al leer de localStorage:', error);
      return defaultValue || null;
    }
  }

  /**
   * Elimina un elemento del almacenamiento local
   */
  removeItem(key: string): boolean {
    if (!this.isAvailable) {
      return false;
    }

    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Error al eliminar de localStorage:', error);
      return false;
    }
  }

  /**
   * Limpia todo el almacenamiento de la aplicación
   */
  clear(): boolean {
    if (!this.isAvailable) {
      return false;
    }

    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      return true;
    } catch (error) {
      console.error('Error al limpiar localStorage:', error);
      return false;
    }
  }

  /**
   * Verifica si el onboarding ha sido completado
   */
  isOnboardingCompleted(): boolean {
    return this.getItem<boolean>(STORAGE_KEYS.ONBOARDING_COMPLETED, false) || false;
  }

  /**
   * Marca el onboarding como completado
   */
  setOnboardingCompleted(completed: boolean = true): boolean {
    return this.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, completed);
  }

  /**
   * Obtiene las preferencias del usuario
   */
  getUserPreferences(): UserPreferences {
    return this.getItem<UserPreferences>(
      STORAGE_KEYS.USER_PREFERENCES, 
      DEFAULT_USER_PREFERENCES
    ) || DEFAULT_USER_PREFERENCES;
  }

  /**
   * Guarda las preferencias del usuario
   */
  setUserPreferences(preferences: Partial<UserPreferences>): boolean {
    const current = this.getUserPreferences();
    const updated = { ...current, ...preferences };
    return this.setItem(STORAGE_KEYS.USER_PREFERENCES, updated);
  }

  /**
   * Obtiene la configuración del tema
   */
  getThemeSettings(): ThemeSettings {
    return this.getItem<ThemeSettings>(
      STORAGE_KEYS.THEME_SETTINGS, 
      DEFAULT_THEME_SETTINGS
    ) || DEFAULT_THEME_SETTINGS;
  }

  /**
   * Guarda la configuración del tema
   */
  setThemeSettings(settings: Partial<ThemeSettings>): boolean {
    const current = this.getThemeSettings();
    const updated = { ...current, ...settings };
    return this.setItem(STORAGE_KEYS.THEME_SETTINGS, updated);
  }

  /**
   * Obtiene la última pestaña activa
   */
  getLastActiveTab(): string {
    return this.getItem<string>(STORAGE_KEYS.LAST_ACTIVE_TAB, 'inicio') || 'inicio';
  }

  /**
   * Guarda la última pestaña activa
   */
  setLastActiveTab(tab: string): boolean {
    return this.setItem(STORAGE_KEYS.LAST_ACTIVE_TAB, tab);
  }

  /**
   * Obtiene el estado de la ventana
   */
  getWindowState(): WindowState {
    return this.getItem<WindowState>(
      STORAGE_KEYS.WINDOW_STATE, 
      DEFAULT_WINDOW_STATE
    ) || DEFAULT_WINDOW_STATE;
  }

  /**
   * Guarda el estado de la ventana
   */
  setWindowState(state: Partial<WindowState>): boolean {
    const current = this.getWindowState();
    const updated = { ...current, ...state };
    return this.setItem(STORAGE_KEYS.WINDOW_STATE, updated);
  }

  /**
   * Exporta todas las configuraciones
   */
  exportSettings(): Record<string, any> {
    const settings: Record<string, any> = {};
    
    Object.values(STORAGE_KEYS).forEach(key => {
      const value = this.getItem(key);
      if (value !== null) {
        settings[key] = value;
      }
    });

    return settings;
  }

  /**
   * Importa configuraciones
   */
  importSettings(settings: Record<string, any>): boolean {
    try {
      Object.entries(settings).forEach(([key, value]) => {
        if (Object.values(STORAGE_KEYS).includes(key as any)) {
          this.setItem(key, value);
        }
      });
      return true;
    } catch (error) {
      console.error('Error al importar configuraciones:', error);
      return false;
    }
  }
}

// Instancia singleton del gestor de almacenamiento
export const storage = new StorageManager();

// Hooks de utilidad para React
export const useStorage = () => {
  return {
    storage,
    isOnboardingCompleted: () => storage.isOnboardingCompleted(),
    setOnboardingCompleted: (completed: boolean) => storage.setOnboardingCompleted(completed),
    getUserPreferences: () => storage.getUserPreferences(),
    setUserPreferences: (prefs: Partial<UserPreferences>) => storage.setUserPreferences(prefs),
    getThemeSettings: () => storage.getThemeSettings(),
    setThemeSettings: (settings: Partial<ThemeSettings>) => storage.setThemeSettings(settings),
    getLastActiveTab: () => storage.getLastActiveTab(),
    setLastActiveTab: (tab: string) => storage.setLastActiveTab(tab),
  };
};

export default storage;
