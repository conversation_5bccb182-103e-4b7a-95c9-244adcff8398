{"rustc": 10895048813736897673, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 16236511956634820608, "deps": [[4899080583175475170, "semver", false, 12698669162100645983], [6913375703034175521, "schemars", false, 6952855851625116018], [7170110829644101142, "json_patch", false, 15278020016385532692], [8786711029710048183, "toml", false, 1016324850575185180], [9689903380558560274, "serde", false, 18397262076831661313], [11050281405049894993, "tauri_utils", false, 5577661854366039437], [12714016054753183456, "tauri_winres", false, 7287275534281892949], [13077543566650298139, "heck", false, 6236491847068976086], [13475171727366188400, "cargo_toml", false, 73095986350446207], [13625485746686963219, "anyhow", false, 11700001983982356752], [15367738274754116744, "serde_json", false, 16284042609359377599], [15622660310229662834, "walkdir", false, 5604292734920322603], [16928111194414003569, "dirs", false, 14986364135949624918], [17155886227862585100, "glob", false, 15406231137402787690]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-45f831d066b647c0\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}