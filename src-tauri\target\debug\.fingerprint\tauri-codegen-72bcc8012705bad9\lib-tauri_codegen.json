{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 10458293153595023085, "deps": [[3060637413840920116, "proc_macro2", false, 8912137790616353475], [3150220818285335163, "url", false, 13544346878586898129], [4899080583175475170, "semver", false, 12698669162100645983], [7170110829644101142, "json_patch", false, 15278020016385532692], [7392050791754369441, "ico", false, 4805278059224830147], [8319709847752024821, "uuid", false, 16227298859139089366], [9689903380558560274, "serde", false, 18397262076831661313], [9857275760291862238, "sha2", false, 14872329733655386886], [10806645703491011684, "thiserror", false, 4474893319039160182], [11050281405049894993, "tauri_utils", false, 5577661854366039437], [12687914511023397207, "png", false, 17313643437940603821], [13077212702700853852, "base64", false, 14860245615036220803], [14132538657330703225, "brotli", false, 9832872640995531574], [15367738274754116744, "serde_json", false, 16284042609359377599], [15622660310229662834, "walkdir", false, 5604292734920322603], [17990358020177143287, "quote", false, 10839489564739371623], [18149961000318489080, "syn", false, 5013364308904172276]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-72bcc8012705bad9\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}