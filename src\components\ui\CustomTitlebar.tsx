/**
 * Componente CustomTitlebar
 * Barra de título personalizada que simula la apariencia nativa del sistema operativo
 * Incluye controles de ventana, menú de aplicación, y funcionalidades avanzadas
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  Minus,
  Square,
  X,
  Maximize2,
  Minimize2,
  ChevronDown,
  Settings,
  RefreshCw,
  Search,
  Bell,
  Menu
} from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';
import {
  minimizeWindow,
  toggleMaximizeWindow,
  closeWindow,
  startDragging
} from '../../lib/tauri/windowControls';

// Importar nuevos componentes y tipos
import { AppIconMenu, useSystemMenuItems } from './AppIconMenu';
import { MenuBar } from './MenuBar';
import { useMenuItems } from '../../hooks/useMenuItems';
import { TitlebarNotificationBadge } from './NotificationBadge';
import { NotificationCenter } from './NotificationCenter';
import { useNotifications } from '../../hooks/useNotifications';
import type { TitlebarConfig } from '../../types/titlebar';

interface CustomTitlebarProps extends Partial<TitlebarConfig> {
  className?: string;
}

/**
 * Componente de botón para controles de ventana
 */
interface WindowControlButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  variant: 'minimize' | 'maximize' | 'close';
  os: 'windows' | 'macos' | 'linux';
  isMaximized?: boolean;
}

const WindowControlButton: React.FC<WindowControlButtonProps> = ({
  onClick,
  children,
  variant,
  os,
  isMaximized = false
}) => {
  // Estilos base para todos los sistemas operativos
  const baseClasses = "flex items-center justify-center transition-all duration-200 ease-in-out";

  // Estilos específicos por sistema operativo - Modernizados
  const osStyles = {
    windows: {
      minimize: "w-11 h-8 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 active:bg-gray-300/90 dark:active:bg-gray-600/90 transition-all duration-150 backdrop-blur-sm",
      maximize: "w-11 h-8 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 active:bg-gray-300/90 dark:active:bg-gray-600/90 transition-all duration-150 backdrop-blur-sm",
      close: "w-11 h-8 hover:bg-red-500/90 hover:text-white active:bg-red-600/95 transition-all duration-150 backdrop-blur-sm"
    },
    macos: {
      minimize: "w-3 h-3 rounded-full bg-yellow-400 hover:bg-yellow-500 active:bg-yellow-600 shadow-sm transition-all duration-150",
      maximize: "w-3 h-3 rounded-full bg-green-400 hover:bg-green-500 active:bg-green-600 shadow-sm transition-all duration-150",
      close: "w-3 h-3 rounded-full bg-red-400 hover:bg-red-500 active:bg-red-600 shadow-sm transition-all duration-150"
    },
    linux: {
      minimize: "w-9 h-8 rounded-md hover:bg-gray-200/80 dark:hover:bg-gray-700/80 active:bg-gray-300/90 dark:active:bg-gray-600/90 transition-all duration-150 backdrop-blur-sm",
      maximize: "w-9 h-8 rounded-md hover:bg-gray-200/80 dark:hover:bg-gray-700/80 active:bg-gray-300/90 dark:active:bg-gray-600/90 transition-all duration-150 backdrop-blur-sm",
      close: "w-9 h-8 rounded-md hover:bg-red-500/90 hover:text-white active:bg-red-600/95 transition-all duration-150 backdrop-blur-sm"
    }
  };

  const buttonClasses = `${baseClasses} ${osStyles[os][variant]}`;

  return (
    <button
      onClick={onClick}
      className={`${buttonClasses} group relative overflow-hidden`}
      aria-label={`${variant} window`}
    >
      {os === 'macos' ? (
        // macOS usa círculos de colores con iconos sutiles
        <span className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          {variant === 'minimize' && <Minus size={8} className="text-yellow-800" />}
          {variant === 'maximize' && (isMaximized ? <Minimize2 size={8} className="text-green-800" /> : <Maximize2 size={8} className="text-green-800" />)}
          {variant === 'close' && <X size={8} className="text-red-800" />}
        </span>
      ) : (
        // Windows/Linux con iconos modernos
        <span className="opacity-70 group-hover:opacity-100 transition-opacity duration-150">
          {children}
        </span>
      )}
    </button>
  );
};

/**
 * Componente principal de la barra de título personalizada
 */
export const CustomTitlebar: React.FC<CustomTitlebarProps> = ({
  title = "Hydra21 App",
  showIcon = true,
  showMenuBar = true,
  enableDoubleClickMaximize = true,
  enableKeyboardShortcuts = true,
  onMenuItemClick,
  className = ""
}) => {
  const {
    os,
    isDark,
    isWindowFocused,
    isWindowMaximized,
    titlebarOpacity
  } = useTheme();

  // Estados locales para la funcionalidad avanzada
  const [showAppIconMenu, setShowAppIconMenu] = useState(false);
  const [appIconMenuPosition, setAppIconMenuPosition] = useState({ x: 0, y: 0 });
  const [lastClickTime, setLastClickTime] = useState(0);
  const [showNotificationCenter, setShowNotificationCenter] = useState(false);

  // Hooks para obtener elementos de menú y notificaciones
  const menuItems = useMenuItems();
  const systemMenuItems = useSystemMenuItems();
  const { unreadCount } = useNotifications();

  // Referencias para elementos del DOM
  const titlebarRef = useRef<HTMLDivElement>(null);
  const appIconRef = useRef<HTMLButtonElement>(null);

  // Manejadores de eventos para los controles de ventana
  const handleMinimize = () => minimizeWindow();
  const handleMaximize = () => toggleMaximizeWindow();
  const handleClose = () => closeWindow();
  const handleDragStart = () => startDragging();

  // Manejador para el doble clic en la titlebar (maximizar/restaurar)
  const handleTitlebarDoubleClick = () => {
    if (enableDoubleClickMaximize) {
      const currentTime = Date.now();
      const timeDiff = currentTime - lastClickTime;

      if (timeDiff < 300) { // 300ms para detectar doble clic
        handleMaximize();
      }

      setLastClickTime(currentTime);
    }
  };

  // Manejador para el clic en el ícono de la aplicación
  const handleAppIconClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();

    if (appIconRef.current) {
      const rect = appIconRef.current.getBoundingClientRect();
      setAppIconMenuPosition({
        x: rect.left,
        y: rect.bottom + 4
      });
      setShowAppIconMenu(!showAppIconMenu);
    }
  };

  // Manejador para elementos del menú
  const handleMenuItemClick = (menuId: string, itemId: string) => {
    if (onMenuItemClick) {
      onMenuItemClick(menuId, itemId);
    }
    console.log(`Menu clicked: ${menuId} -> ${itemId}`);
  };

  // Manejador para el centro de notificaciones
  const handleNotificationCenterToggle = () => {
    setShowNotificationCenter(!showNotificationCenter);
  };

  // Efecto para manejar atajos de teclado
  useEffect(() => {
    if (!enableKeyboardShortcuts) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + Space para abrir menú del sistema (Windows)
      if (event.altKey && event.code === 'Space' && os === 'windows') {
        event.preventDefault();
        if (appIconRef.current) {
          const rect = appIconRef.current.getBoundingClientRect();
          setAppIconMenuPosition({
            x: rect.left,
            y: rect.bottom + 4
          });
          setShowAppIconMenu(true);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [enableKeyboardShortcuts, os]);

  // Estilos base de la titlebar (altura ajustada para incluir menú)
  const titlebarClasses = `
    flex items-center justify-between px-2 select-none
    ${showMenuBar && os !== 'macos' ? 'h-10' : 'h-8'}
    ${isDark
      ? 'bg-gray-900/90 border-gray-700/50 text-gray-100'
      : 'bg-white/90 border-gray-200/50 text-gray-900'
    }
    backdrop-blur-md border-b shadow-sm
    transition-all duration-300 ease-in-out
    ${className}
  `.trim();

  // Área de arrastre (varía según el OS)
  const dragAreaClasses = `
    flex items-center flex-1 h-full cursor-move
    ${os === 'macos' ? 'justify-center' : 'justify-start pl-2'}
  `;

  // Controles de ventana según el sistema operativo
  const renderWindowControls = () => {
    const controls = [
      {
        variant: 'minimize' as const,
        icon: <Minus size={14} className="stroke-[1.5]" />,
        handler: handleMinimize
      },
      {
        variant: 'maximize' as const,
        icon: isWindowMaximized ? <Square size={12} className="stroke-[1.5]" /> : <Square size={14} className="stroke-[1.5]" />,
        handler: handleMaximize
      },
      {
        variant: 'close' as const,
        icon: <X size={14} className="stroke-[1.5]" />,
        handler: handleClose
      }
    ];

    if (os === 'macos') {
      // macOS: controles a la izquierda, orden: close, minimize, maximize
      const macControls = [controls[2], controls[0], controls[1]];
      return (
        <div className="flex items-center space-x-2 pl-2 group">
          {macControls.map((control, index) => (
            <WindowControlButton
              key={index}
              onClick={control.handler}
              variant={control.variant}
              os={os}
              isMaximized={isWindowMaximized}
            >
              {control.icon}
            </WindowControlButton>
          ))}
        </div>
      );
    } else {
      // Windows/Linux: controles a la derecha
      return (
        <div className="flex items-center">
          {controls.map((control, index) => (
            <WindowControlButton
              key={index}
              onClick={control.handler}
              variant={control.variant}
              os={os}
              isMaximized={isWindowMaximized}
            >
              {control.icon}
            </WindowControlButton>
          ))}
        </div>
      );
    }
  };

  return (
    <div className="flex flex-col relative z-40">
      {/* Titlebar principal con menú integrado */}
      <div
        ref={titlebarRef}
        className={`${titlebarClasses} relative z-40`}
        style={{ opacity: titlebarOpacity }}
        onClick={handleTitlebarDoubleClick}
      >
        {/* Controles de ventana para macOS (izquierda) */}
        {os === 'macos' && renderWindowControls()}

        {/* Área izquierda: Ícono + Menú + Título */}
        <div className="flex items-center flex-1">
          {showIcon && (
            <button
              ref={appIconRef}
              onClick={handleAppIconClick}
              className={`
                flex items-center justify-center w-6 h-6 mr-2 rounded-sm transition-colors
                hover:bg-gray-200 dark:hover:bg-gray-700 active:bg-gray-300 dark:active:bg-gray-600
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
              `}
              aria-label="Menú de aplicación"
              title="Clic para abrir menú de aplicación"
            >
              <img
                src="/pipe_logo.svg"
                alt="Hydra21 Logo"
                className="w-5 h-5"
                style={{ filter: isDark ? 'brightness(0) invert(1)' : 'none' }}
              />
            </button>
          )}

          {/* Barra de menú integrada (solo para Windows/Linux) */}
          {showMenuBar && os !== 'macos' && (
            <div className="flex items-center mr-4">
              <MenuBar
                items={menuItems}
                onMenuItemClick={handleMenuItemClick}
                className="flex items-center"
              />
            </div>
          )}

          {/* Área de arrastre con título */}
          <div
            className={`
              flex items-center flex-1 h-full cursor-move
              ${os === 'macos' ? 'justify-center' : 'justify-center'}
            `}
            onMouseDown={handleDragStart}
          >
            <span className={`
              text-sm font-medium truncate
              ${!isWindowFocused ? 'opacity-60' : ''}
              ${os === 'macos' ? 'text-center' : 'text-center'}
            `}>
              {title}
            </span>
          </div>
        </div>

        {/* Área derecha: Notificaciones + Controles de ventana */}
        <div className="flex items-center">
          {/* Badge de notificaciones (solo para Windows/Linux) */}
          {os !== 'macos' && (
            <div className="mr-2">
              <TitlebarNotificationBadge
                count={unreadCount}
                onClick={handleNotificationCenterToggle}
                isActive={showNotificationCenter}
              />
            </div>
          )}

          {/* Controles de ventana para Windows/Linux */}
          {os !== 'macos' && renderWindowControls()}
        </div>
      </div>

      {/* Barra de menú para macOS (debajo de la titlebar, como es nativo) */}
      {showMenuBar && os === 'macos' && (
        <div className={`
          px-2 py-1 border-b
          ${isDark
            ? 'bg-gray-900/95 border-gray-700/50'
            : 'bg-white/95 border-gray-200/50'
          }
          backdrop-blur-sm
        `}>
          <MenuBar
            items={menuItems}
            onMenuItemClick={handleMenuItemClick}
          />
        </div>
      )}

      {/* Menú contextual del ícono de aplicación */}
      <AppIconMenu
        isOpen={showAppIconMenu}
        onClose={() => setShowAppIconMenu(false)}
        items={systemMenuItems}
        position={appIconMenuPosition}
      />

      {/* Centro de notificaciones */}
      <NotificationCenter
        isOpen={showNotificationCenter}
        onClose={() => setShowNotificationCenter(false)}
        position="right"
      />
    </div>
  );
};
