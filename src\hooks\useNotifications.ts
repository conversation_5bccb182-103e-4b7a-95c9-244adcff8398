/**
 * Hook personalizado para el sistema de notificaciones
 * Proporciona una interfaz React para el store de notificaciones
 */

import { useEffect, useState } from 'react';
import { notificationStore, subscribeToNotifications } from '../store/notificationStore';
import type {
  Notification,
  ToastNotification,
  NotificationType,
  NotificationSettings
} from '../types/notifications';

interface UseNotificationsReturn {
  // Estado
  notifications: Notification[];
  toasts: ToastNotification[];
  unreadCount: number;
  isOpen: boolean;
  filter: NotificationType | 'all';
  sortBy: 'newest' | 'oldest' | 'type';
  settings: NotificationSettings;

  // Acciones principales
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => string;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  clearByType: (type: NotificationType) => void;

  // Toast específicos
  showToast: (toast: Omit<ToastNotification, 'id' | 'timestamp'>) => string;
  hideToast: (id: string) => void;

  // Centro de notificaciones
  toggleCenter: () => void;
  setFilter: (filter: NotificationType | 'all') => void;
  setSortBy: (sortBy: 'newest' | 'oldest' | 'type') => void;

  // Configuración
  updateSettings: (settings: Partial<NotificationSettings>) => void;

  // Helpers
  getFilteredNotifications: () => Notification[];
  getSortedNotifications: (notifications: Notification[]) => Notification[];

  // Métodos de conveniencia
  showSuccess: (title: string, message: string) => string;
  showError: (title: string, message: string) => string;
  showWarning: (title: string, message: string) => string;
  showInfo: (title: string, message: string) => string;
}

/**
 * Hook principal para notificaciones
 */
export const useNotifications = (): UseNotificationsReturn => {
  // Estado local para forzar re-renders
  const [, forceUpdate] = useState({});

  // Efecto para suscribirse a cambios del store
  useEffect(() => {
    // Inicializar el store
    notificationStore.initialize();

    // Suscribirse a cambios
    const unsubscribe = subscribeToNotifications(() => {
      forceUpdate({});
    });

    // Cleanup
    return unsubscribe;
  }, []);

  // Helper para filtrar notificaciones
  const getFilteredNotifications = (): Notification[] => {
    const { notifications, center } = notificationStore;

    if (center.filter === 'all') {
      return notifications;
    }

    return notifications.filter(n => n.type === center.filter);
  };

  // Helper para ordenar notificaciones
  const getSortedNotifications = (notifications: Notification[]): Notification[] => {
    const { center } = notificationStore;

    switch (center.sortBy) {
      case 'newest':
        return [...notifications].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      case 'oldest':
        return [...notifications].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      case 'type':
        return [...notifications].sort((a, b) => a.type.localeCompare(b.type));
      default:
        return notifications;
    }
  };

  // Métodos de conveniencia para tipos específicos
  const showSuccess = (title: string, message: string): string => {
    return notificationStore.showToast({
      type: 'success',
      title,
      message,
    });
  };

  const showError = (title: string, message: string): string => {
    return notificationStore.showToast({
      type: 'error',
      title,
      message,
    });
  };

  const showWarning = (title: string, message: string): string => {
    return notificationStore.showToast({
      type: 'warning',
      title,
      message,
    });
  };

  const showInfo = (title: string, message: string): string => {
    return notificationStore.showToast({
      type: 'info',
      title,
      message,
    });
  };

  return {
    // Estado
    notifications: notificationStore.notifications,
    toasts: notificationStore.toasts,
    unreadCount: notificationStore.unreadCount,
    isOpen: notificationStore.center.isOpen,
    filter: notificationStore.center.filter,
    sortBy: notificationStore.center.sortBy,
    settings: notificationStore.settings,

    // Acciones principales
    addNotification: notificationStore.addNotification,
    removeNotification: notificationStore.removeNotification,
    markAsRead: notificationStore.markAsRead,
    markAllAsRead: notificationStore.markAllAsRead,
    clearAll: notificationStore.clearAll,
    clearByType: notificationStore.clearByType,

    // Toast específicos
    showToast: notificationStore.showToast,
    hideToast: notificationStore.hideToast,

    // Centro de notificaciones
    toggleCenter: notificationStore.toggleCenter,
    setFilter: notificationStore.setFilter,
    setSortBy: notificationStore.setSortBy,

    // Configuración
    updateSettings: notificationStore.updateSettings,

    // Helpers
    getFilteredNotifications,
    getSortedNotifications,

    // Métodos de conveniencia
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
};

/**
 * Hook simplificado solo para mostrar toasts
 */
export const useToast = () => {
  const { showToast, hideToast, showSuccess, showError, showWarning, showInfo } = useNotifications();

  return {
    showToast,
    hideToast,
    success: showSuccess,
    error: showError,
    warning: showWarning,
    info: showInfo,
  };
};

/**
 * Hook para el centro de notificaciones
 */
export const useNotificationCenter = () => {
  const {
    unreadCount,
    isOpen,
    filter,
    sortBy,
    toggleCenter,
    setFilter,
    setSortBy,
    markAsRead,
    markAllAsRead,
    removeNotification,
    getFilteredNotifications,
    getSortedNotifications,
  } = useNotifications();

  const filteredNotifications = getFilteredNotifications();
  const sortedNotifications = getSortedNotifications(filteredNotifications);

  return {
    notifications: sortedNotifications,
    unreadCount,
    isOpen,
    filter,
    sortBy,
    toggleCenter,
    setFilter,
    setSortBy,
    markAsRead,
    markAllAsRead,
    removeNotification,
  };
};
